import pandas as pd
import numpy as np
import baostock as bs
from datetime import datetime, timedelta
import warnings
import time
import os
warnings.filterwarnings('ignore')

class StockScreener:
    def __init__(self, window_size=20, start_date=None, end_date=None):
        """
        初始化股票筛选器
        :param window_size: 滑动窗口大小N
        :param start_date: 开始日期，格式：'YYYY-MM-DD'
        :param end_date: 结束日期，格式：'YYYY-MM-DD'
        """
        self.window_size = window_size
        self.results = []
        self.data_cache = {}  # 数据缓存
        self.bs_logged_in = False

        # 设置默认日期范围（最近一年）
        if end_date is None:
            self.end_date = datetime.now().strftime('%Y-%m-%d')
        else:
            self.end_date = end_date

        if start_date is None:
            start_dt = datetime.now() - timedelta(days=365)
            self.start_date = start_dt.strftime('%Y-%m-%d')
        else:
            self.start_date = start_date
    
    def load_stock_codes(self, file_path):
        """
        读取股票代码文件
        :param file_path: Excel文件路径
        :return: 股票代码列表
        """
        try:
            df = pd.read_excel(file_path)
            # 假设股票代码在第一列，根据实际情况调整列名
            if 'code' in df.columns:
                stock_codes = df['code'].tolist()
            elif 'stock_code' in df.columns:
                stock_codes = df['stock_code'].tolist()
            else:
                # 如果列名不确定，取第一列
                stock_codes = df.iloc[:, 0].tolist()
            
            # 处理股票代码格式，确保是字符串格式
            stock_codes = [str(code).zfill(6) for code in stock_codes if pd.notna(code)]
            print(f"成功读取 {len(stock_codes)} 只股票代码")
            return stock_codes
        except Exception as e:
            print(f"读取股票代码文件失败: {e}")
            return []
    
    def login_baostock(self):
        """登录baostock"""
        if not self.bs_logged_in:
            lg = bs.login()
            if lg.error_code != '0':
                print(f'baostock登录失败: {lg.error_msg}')
                return False
            self.bs_logged_in = True
            print("baostock登录成功")
        return True

    def logout_baostock(self):
        """登出baostock"""
        if self.bs_logged_in:
            bs.logout()
            self.bs_logged_in = False
            print("baostock登出成功")

    def format_stock_code(self, stock_code):
        """
        格式化股票代码为baostock格式
        :param stock_code: 原始股票代码
        :return: baostock格式的股票代码
        """
        stock_code = str(stock_code).zfill(6)
        if stock_code.startswith('6'):
            return f"sh.{stock_code}"  # 上海交易所
        elif stock_code.startswith(('0', '3')):
            return f"sz.{stock_code}"  # 深圳交易所
        else:
            return f"sz.{stock_code}"  # 默认深圳

    def get_stock_data(self, stock_code):
        """
        获取单只股票数据
        :param stock_code: 股票代码
        :return: 股票数据DataFrame
        """
        # 检查缓存
        if stock_code in self.data_cache:
            return self.data_cache[stock_code]

        if not self.login_baostock():
            return None

        try:
            bs_code = self.format_stock_code(stock_code)

            # 获取日K线数据
            rs = bs.query_history_k_data_plus(
                bs_code,
                "date,open,high,low,close,volume,amount",
                start_date=self.start_date,
                end_date=self.end_date,
                frequency="d",
                adjustflag="3"  # 不复权
            )

            if rs.error_code != '0':
                print(f"获取股票 {stock_code} 数据失败: {rs.error_msg}")
                return None

            # 转换为DataFrame
            data_list = []
            while (rs.error_code == '0') & rs.next():
                data_list.append(rs.get_row_data())

            if not data_list:
                print(f"股票 {stock_code} 无数据")
                return None

            df = pd.DataFrame(data_list, columns=rs.fields)

            # 数据类型转换
            df['date'] = pd.to_datetime(df['date'])
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            # 设置日期为索引
            df.set_index('date', inplace=True)

            # 重命名列以匹配原有逻辑
            df.rename(columns={
                'open': 'Open',
                'high': 'High',
                'low': 'Low',
                'close': 'Close',
                'volume': 'Volume'
            }, inplace=True)

            # 过滤无效数据
            df = df.dropna()

            if df.empty:
                print(f"股票 {stock_code} 数据为空")
                return None

            # 缓存数据
            self.data_cache[stock_code] = df

            return df

        except Exception as e:
            print(f"获取股票 {stock_code} 数据失败: {e}")
            return None
    
    def calculate_sliding_windows(self, data):
        """
        计算滑动窗口的平均成交量、最大成交量和价格信息
        :param data: 股票数据
        :return: 窗口信息列表
        """
        if len(data) < self.window_size:
            return []
        
        windows = []
        for i in range(self.window_size // 2, len(data) - self.window_size // 2):
            start_idx = i - self.window_size // 2
            end_idx = i + self.window_size // 2 + 1
            
            window_data = data.iloc[start_idx:end_idx]
            
            avg_volume = window_data['Volume'].mean()
            max_volume = window_data['Volume'].max()
            min_price = window_data['Low'].min()
            max_price = window_data['High'].max()
            
            # 计算峰均比 (最大成交量/平均成交量)
            peak_avg_ratio = max_volume / avg_volume if avg_volume > 0 else 0
            
            window_info = {
                'window_index': i,
                'avg_volume': avg_volume,
                'max_volume': max_volume,
                'peak_avg_ratio': peak_avg_ratio,
                'min_price': min_price,
                'max_price': max_price,
                'center_date': data.index[i]
            }
            windows.append(window_info)
        
        return windows
    
    def screen_stock(self, stock_code):
        """
        对单只股票进行筛选
        :param stock_code: 股票代码
        :return: 是否满足条件
        """
        # 从缓存获取股票数据
        if stock_code not in self.data_cache:
            print(f"股票 {stock_code} 数据未缓存，跳过")
            return False

        data = self.data_cache[stock_code]
        if data is None or data.empty:
            return False
        
        # 计算滑动窗口
        windows = self.calculate_sliding_windows(data)
        if len(windows) < 3 * self.window_size:
            print(f"股票 {stock_code} 数据不足，跳过")
            return False
        
        # 以步长N遍历窗序列
        for i in range(0, len(windows) - 2 * self.window_size, self.window_size):
            if i + 2 * self.window_size >= len(windows):
                break
            
            window_a = windows[i]
            window_b = windows[i + self.window_size]
            window_c = windows[i + 2 * self.window_size]
            
            # 提取条件参数
            a1 = window_a['avg_volume']
            a2 = window_a['peak_avg_ratio']

            b1 = window_b['avg_volume']
            b2 = window_b['peak_avg_ratio']

            c1 = window_c['avg_volume']
            c2 = window_c['peak_avg_ratio']
            
            # 筛选条件（根据您的要求调整）
            # 条件：a2>2*a1, a2>a3, a3>a1; b3>1.5*b1; 窗A最低价>窗B最低价
            # 注：由于a2是峰均比，a1是平均成交量，这里按业务逻辑调整

            condition1 = a2 > 2.0  # 窗A峰均比大于2
            condition2 = a2 > c2   # 窗A峰均比大于窗C峰均比
            condition3 = c2 > 1.0  # 窗C峰均比大于1
            condition4 = b2 > 1.5  # 窗B峰均比大于1.5
            condition5 = window_a['min_price'] > window_b['min_price']  # 窗A最低价>窗B最低价

            if (condition1 and condition2 and condition3 and
                condition4 and condition5):
                
                result = {
                    'stock_code': stock_code,
                    'window_a_index': window_a['window_index'],
                    'window_b_index': window_b['window_index'],
                    'window_c_index': window_c['window_index'],
                    'window_a_date': window_a['center_date'],
                    'window_b_date': window_b['center_date'],
                    'window_c_date': window_c['center_date'],
                    'a_avg_volume': a1,
                    'a_peak_avg_ratio': a2,
                    'b_avg_volume': b1,
                    'b_peak_avg_ratio': b2,
                    'c_avg_volume': c1,
                    'c_peak_avg_ratio': c2,
                    'a_min_price': window_a['min_price'],
                    'b_min_price': window_b['min_price'],
                    'condition_met': True
                }
                self.results.append(result)
                print(f"股票 {stock_code} 满足条件！")
                return True
        
        return False
    
    def batch_get_stock_data(self, stock_codes, batch_size=50):
        """
        批量获取股票数据以减少请求次数
        :param stock_codes: 股票代码列表
        :param batch_size: 批次大小
        """
        print(f"开始批量获取 {len(stock_codes)} 只股票数据...")

        if not self.login_baostock():
            return

        success_count = 0
        failed_codes = []

        for i in range(0, len(stock_codes), batch_size):
            batch_codes = stock_codes[i:i + batch_size]
            print(f"正在获取第 {i//batch_size + 1} 批数据 ({len(batch_codes)} 只股票)...")

            for stock_code in batch_codes:
                try:
                    data = self.get_stock_data(stock_code)
                    if data is not None:
                        success_count += 1
                    else:
                        failed_codes.append(stock_code)

                    # 添加小延时避免请求过快
                    time.sleep(0.1)

                except Exception as e:
                    print(f"获取股票 {stock_code} 数据失败: {e}")
                    failed_codes.append(stock_code)

            # 批次间稍长延时
            if i + batch_size < len(stock_codes):
                time.sleep(1)

        print(f"数据获取完成！成功: {success_count}, 失败: {len(failed_codes)}")
        if failed_codes:
            print(f"失败的股票代码: {failed_codes[:10]}{'...' if len(failed_codes) > 10 else ''}")

    def run_screening(self, stock_file_path, output_file_path):
        """
        运行股票筛选
        :param stock_file_path: 股票代码文件路径
        :param output_file_path: 输出文件路径
        """
        print("开始股票筛选...")

        try:
            # 读取股票代码
            stock_codes = self.load_stock_codes(stock_file_path)
            if not stock_codes:
                print("未能读取到股票代码，程序退出")
                return

            # 批量获取数据
            self.batch_get_stock_data(stock_codes)

            # 筛选股票
            print("开始筛选分析...")
            qualified_count = 0
            processed_count = 0

            for stock_code in stock_codes:
                try:
                    processed_count += 1
                    if processed_count % 50 == 0:
                        print(f"已处理 {processed_count}/{len(stock_codes)} 只股票")

                    if self.screen_stock(stock_code):
                        qualified_count += 1

                except Exception as e:
                    print(f"处理股票 {stock_code} 时出错: {e}")
                    continue

            # 保存结果
            if self.results:
                df_results = pd.DataFrame(self.results)
                df_results.to_excel(output_file_path, index=False)
                print(f"筛选完成！共找到 {qualified_count} 只满足条件的股票")
                print(f"结果已保存到: {output_file_path}")
            else:
                print("未找到满足条件的股票")

        finally:
            # 确保登出baostock
            self.logout_baostock()

def main():
    # 设置参数
    window_size = 20  # 窗口大小N
    stock_file_path = r"D:\edge下载\量化文档\stock_pool_20250725.xlsx"
    output_file_path = "qualified_stocks.xlsx"

    # 设置日期范围（可根据需要调整）
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')

    print(f"分析时间范围: {start_date} 到 {end_date}")
    print(f"滑动窗口大小: {window_size}")
    print(f"股票池文件: {stock_file_path}")
    print(f"输出文件: {output_file_path}")
    print("-" * 50)

    # 创建筛选器并运行
    screener = StockScreener(
        window_size=window_size,
        start_date=start_date,
        end_date=end_date
    )
    screener.run_screening(stock_file_path, output_file_path)

if __name__ == "__main__":
    main()
