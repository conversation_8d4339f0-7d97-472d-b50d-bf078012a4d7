import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class StockScreener:
    def __init__(self, window_size=20):
        """
        初始化股票筛选器
        :param window_size: 滑动窗口大小N
        """
        self.window_size = window_size
        self.results = []
    
    def load_stock_codes(self, file_path):
        """
        读取股票代码文件
        :param file_path: Excel文件路径
        :return: 股票代码列表
        """
        try:
            df = pd.read_excel(file_path)
            # 假设股票代码在第一列，根据实际情况调整列名
            if 'code' in df.columns:
                stock_codes = df['code'].tolist()
            elif 'stock_code' in df.columns:
                stock_codes = df['stock_code'].tolist()
            else:
                # 如果列名不确定，取第一列
                stock_codes = df.iloc[:, 0].tolist()
            
            # 处理股票代码格式，确保是字符串格式
            stock_codes = [str(code).zfill(6) for code in stock_codes if pd.notna(code)]
            print(f"成功读取 {len(stock_codes)} 只股票代码")
            return stock_codes
        except Exception as e:
            print(f"读取股票代码文件失败: {e}")
            return []
    
    def get_stock_data(self, stock_code, period="1y"):
        """
        获取股票数据
        :param stock_code: 股票代码
        :param period: 数据周期
        :return: 股票数据DataFrame
        """
        try:
            # 为A股添加后缀
            if stock_code.startswith('6'):
                symbol = f"{stock_code}.SS"  # 上海交易所
            elif stock_code.startswith(('0', '3')):
                symbol = f"{stock_code}.SZ"  # 深圳交易所
            else:
                symbol = stock_code
            
            stock = yf.Ticker(symbol)
            data = stock.history(period=period)
            
            if data.empty:
                print(f"无法获取股票 {stock_code} 的数据")
                return None
            
            return data
        except Exception as e:
            print(f"获取股票 {stock_code} 数据失败: {e}")
            return None
    
    def calculate_sliding_windows(self, data):
        """
        计算滑动窗口的平均成交量、最大成交量和价格信息
        :param data: 股票数据
        :return: 窗口信息列表
        """
        if len(data) < self.window_size:
            return []
        
        windows = []
        for i in range(self.window_size // 2, len(data) - self.window_size // 2):
            start_idx = i - self.window_size // 2
            end_idx = i + self.window_size // 2 + 1
            
            window_data = data.iloc[start_idx:end_idx]
            
            avg_volume = window_data['Volume'].mean()
            max_volume = window_data['Volume'].max()
            min_price = window_data['Low'].min()
            max_price = window_data['High'].max()
            
            # 计算峰均比 (最大成交量/平均成交量)
            peak_avg_ratio = max_volume / avg_volume if avg_volume > 0 else 0
            
            window_info = {
                'window_index': i,
                'avg_volume': avg_volume,
                'max_volume': max_volume,
                'peak_avg_ratio': peak_avg_ratio,
                'min_price': min_price,
                'max_price': max_price,
                'center_date': data.index[i]
            }
            windows.append(window_info)
        
        return windows
    
    def screen_stock(self, stock_code):
        """
        对单只股票进行筛选
        :param stock_code: 股票代码
        :return: 是否满足条件
        """
        print(f"正在分析股票: {stock_code}")
        
        # 获取股票数据
        data = self.get_stock_data(stock_code)
        if data is None:
            return False
        
        # 计算滑动窗口
        windows = self.calculate_sliding_windows(data)
        if len(windows) < 3 * self.window_size:
            print(f"股票 {stock_code} 数据不足，跳过")
            return False
        
        # 以步长N遍历窗序列
        for i in range(0, len(windows) - 2 * self.window_size, self.window_size):
            if i + 2 * self.window_size >= len(windows):
                break
            
            window_a = windows[i]
            window_b = windows[i + self.window_size]
            window_c = windows[i + 2 * self.window_size]
            
            # 提取条件参数
            a1 = window_a['avg_volume']
            a2 = window_a['peak_avg_ratio']
            a3 = window_a['min_price']  # 假设a3是窗A的最低价
            
            b1 = window_b['avg_volume']
            b2 = window_b['peak_avg_ratio']
            b3 = window_b['peak_avg_ratio']  # 根据描述，b3应该是峰均比
            
            c1 = window_c['avg_volume']
            c2 = window_c['peak_avg_ratio']
            
            # 检查条件
            condition1 = a2 > 2 * a1  # a2 > 2*a1 (这里可能有误，因为a2是比值，a1是成交量)
            condition2 = a2 > a3      # a2 > a3
            condition3 = a3 > a1      # a3 > a1 (这里可能有误，因为a3是价格，a1是成交量)
            condition4 = b3 > 1.5 * b1  # b3 > 1.5*b1
            condition5 = window_a['min_price'] > window_b['min_price']  # 窗A最低价 > 窗B最低价
            
            # 修正条件逻辑（根据实际业务逻辑调整）
            # 假设条件应该是：
            condition1_fixed = a2 > 2.0  # 峰均比大于2
            condition2_fixed = a2 > c2   # 窗A峰均比大于窗C峰均比
            condition3_fixed = c2 > 1.0  # 窗C峰均比大于1
            condition4_fixed = b2 > 1.5  # 窗B峰均比大于1.5
            condition5_fixed = window_a['min_price'] > window_b['min_price']
            
            if (condition1_fixed and condition2_fixed and condition3_fixed and 
                condition4_fixed and condition5_fixed):
                
                result = {
                    'stock_code': stock_code,
                    'window_a_index': window_a['window_index'],
                    'window_b_index': window_b['window_index'],
                    'window_c_index': window_c['window_index'],
                    'window_a_date': window_a['center_date'],
                    'window_b_date': window_b['center_date'],
                    'window_c_date': window_c['center_date'],
                    'a_avg_volume': a1,
                    'a_peak_avg_ratio': a2,
                    'b_avg_volume': b1,
                    'b_peak_avg_ratio': b2,
                    'c_avg_volume': c1,
                    'c_peak_avg_ratio': c2,
                    'a_min_price': window_a['min_price'],
                    'b_min_price': window_b['min_price'],
                    'condition_met': True
                }
                self.results.append(result)
                print(f"股票 {stock_code} 满足条件！")
                return True
        
        return False
    
    def run_screening(self, stock_file_path, output_file_path):
        """
        运行股票筛选
        :param stock_file_path: 股票代码文件路径
        :param output_file_path: 输出文件路径
        """
        print("开始股票筛选...")
        
        # 读取股票代码
        stock_codes = self.load_stock_codes(stock_file_path)
        if not stock_codes:
            print("未能读取到股票代码，程序退出")
            return
        
        # 筛选股票
        qualified_count = 0
        for stock_code in stock_codes:
            try:
                if self.screen_stock(stock_code):
                    qualified_count += 1
            except Exception as e:
                print(f"处理股票 {stock_code} 时出错: {e}")
                continue
        
        # 保存结果
        if self.results:
            df_results = pd.DataFrame(self.results)
            df_results.to_excel(output_file_path, index=False)
            print(f"筛选完成！共找到 {qualified_count} 只满足条件的股票")
            print(f"结果已保存到: {output_file_path}")
        else:
            print("未找到满足条件的股票")

def main():
    # 设置参数
    window_size = 20  # 窗口大小N
    stock_file_path = r"D:\edge下载\量化文档\stock_pool_20250725.xlsx"
    output_file_path = "qualified_stocks.xlsx"
    
    # 创建筛选器并运行
    screener = StockScreener(window_size=window_size)
    screener.run_screening(stock_file_path, output_file_path)

if __name__ == "__main__":
    main()
