"""
测试绘图功能的简单脚本
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def test_plot():
    """测试绘图功能"""
    # 创建模拟数据
    dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')
    n = len(dates)
    
    # 生成模拟股价数据
    np.random.seed(42)
    price_base = 10
    price_changes = np.random.normal(0, 0.02, n)
    prices = [price_base]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 1))  # 确保价格不为负
    
    # 创建OHLC数据
    data = pd.DataFrame(index=dates)
    data['Close'] = prices
    data['Open'] = data['Close'].shift(1).fillna(data['Close'].iloc[0])
    data['High'] = data[['Open', 'Close']].max(axis=1) * (1 + np.random.uniform(0, 0.05, n))
    data['Low'] = data[['Open', 'Close']].min(axis=1) * (1 - np.random.uniform(0, 0.05, n))
    data['Volume'] = np.random.randint(1000000, 10000000, n)
    
    # 创建简单的图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), gridspec_kw={'height_ratios': [3, 1]})
    
    # 绘制价格线
    ax1.plot(data.index, data['Close'], label='收盘价', linewidth=1)
    ax1.set_title('测试股票价格图', fontsize=14)
    ax1.set_ylabel('价格', fontsize=12)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 绘制成交量
    ax2.bar(data.index, data['Volume'], alpha=0.7, width=0.8)
    ax2.set_title('成交量', fontsize=14)
    ax2.set_ylabel('成交量', fontsize=12)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('test_chart.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("测试图表已保存为 test_chart.png")

if __name__ == "__main__":
    test_plot()
