# 庄家股筛选工具

## 功能概述

这是一个基于滑动窗口技术分析的股票筛选工具，能够识别满足特定条件的股票，并自动生成详细的分析图表。

## 主要功能

1. **股票数据获取**：使用baostock获取A股历史数据
2. **滑动窗口分析**：计算窗口内的平均成交量、最大成交量、峰均比等指标
3. **条件筛选**：根据设定的条件筛选满足要求的股票
4. **图表生成**：为满足条件的股票自动生成K线图和成交量分析图

## 筛选条件

程序会寻找满足以下条件的股票：
- 窗口A的峰均比 > 2.0
- 窗口A的峰均比 > 窗口C的峰均比
- 窗口C的峰均比 > 1.0
- 窗口B的峰均比 > 1.5
- 窗口A的最低价 > 窗口B的最低价

其中窗口A、B、C的中心位置间距为N（窗口大小）。

## 图表功能

对于每只满足条件的股票，程序会自动生成包含以下内容的分析图表：

### K线图部分
- 完整的K线图（红色上涨，绿色下跌）
- 三个分析窗口的范围标注（不同颜色的背景区域）
- 各窗口的中心位置标线
- 各窗口内最低点的位置标记

### 成交量图部分
- 成交量柱状图（颜色与K线对应）
- 窗口范围的背景标注
- 窗口中心位置标线

### 信息标注
- 窗口大小信息
- 各窗口的峰均比数值
- 各窗口的最低价数值

## 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install pandas numpy baostock openpyxl matplotlib mplfinance
```

## 使用方法

1. 准备股票代码Excel文件（默认路径：`D:\edge下载\量化文档\stock_pool_20250725.xlsx`）
2. 运行主程序：
   ```bash
   python main.py
   ```
3. 程序会自动：
   - 读取股票代码
   - 批量获取股票数据
   - 进行条件筛选
   - 为满足条件的股票生成图表
   - 保存筛选结果到Excel文件

## 输出文件

- `qualified_stocks.xlsx`：满足条件的股票列表及详细数据
- `charts/`目录：包含所有满足条件股票的分析图表
  - 文件命名格式：`{股票代码}_analysis.png`

## 参数配置

可以在`main.py`中调整以下参数：
- `window_size`：滑动窗口大小（默认20）
- `start_date`和`end_date`：分析的时间范围
- 筛选条件的阈值

## 性能优化

- 使用数据缓存机制减少重复请求
- 批量获取数据降低网络请求频率
- 添加适当延时避免请求过快

## 注意事项

1. 首次运行需要联网获取股票数据
2. 图表生成需要安装中文字体支持
3. 建议在股市交易时间外运行以获得更稳定的数据
4. 大量股票的分析可能需要较长时间

## 测试

可以运行测试脚本验证绘图功能：
```bash
python test_plot.py
```
